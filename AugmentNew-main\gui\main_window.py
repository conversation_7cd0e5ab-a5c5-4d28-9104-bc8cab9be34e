"""
主GUI窗口类
"""

import tkinter as tk
import customtkinter as ctk
from tkinter import messagebox
import os
import sys
import threading

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.paths import (
    get_home_dir, get_app_data_dir, get_storage_path,
    get_db_path, get_machine_id_path, get_workspace_storage_path
)
from utils.version_checker import check_for_updates, get_current_version, get_update_url
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data
from augutils.workspace_cleaner import clean_workspace_storage
from augutils.backup_cleaner import find_backup_files, clean_all_backups
from augutils.advanced_cleaner import AdvancedCleaner
from augutils.data_recovery import DataRecovery

# 导入新的增强模块
from utils.browser_cleaner import BrowserCleaner
from utils.storage_fix import StorageFixer
from utils.github_enhanced import GitHubEnhancedCleaner
from utils.device_fingerprint_cleaner import DeviceFingerprintCleaner
from utils.augment_account_resetter import AugmentAccountResetter
from utils.super_reset_engine import SuperResetEngine

from .styles import COLORS, FONTS, SIZES
from .components import StatusCard, ProgressCard, LogTextBox, ActionButton, ThreadedOperation


class MainWindow(ctk.CTk):
    """主窗口类"""

    def __init__(self, super_config=None):
        super().__init__()

        # 保存超级配置
        self.super_config = super_config or {
            'super_mode': False,
            'all_features_enabled': False,
            'admin_privileges': False
        }
        
        # 窗口配置
        if self.super_config['super_mode']:
            title = "🚀 AugmentNew 超级版 - 所有功能已激活！"
            if self.super_config['admin_privileges']:
                title += " 👑 管理员模式"
        else:
            title = "AugmentNew 免费版 - 永久免费，拒绝收费！"
        self.title(title)
        self.geometry(f"{SIZES['window_width']}x{SIZES['window_height']}")
        self.resizable(True, True)

        # 初始化变量
        self.current_operation = None
        self.update_available = False
        self.latest_version = None

        # 创建界面
        self.create_widgets()
        self.update_system_info()

        # 绑定窗口关闭事件
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 在界面创建完成后设置图标、最大化和检查更新
        self.after(50, self.set_window_icon)
        self.after(100, self.maximize_window)
        self.after(1000, self.check_for_updates_async)

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 尝试多种可能的图标路径
            icon_paths = [
                "favicon.ico",  # 当前目录
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "favicon.ico"),  # 上级目录
                os.path.join(sys._MEIPASS, "favicon.ico") if hasattr(sys, '_MEIPASS') else None,  # PyInstaller打包后的路径
            ]

            for icon_path in icon_paths:
                if icon_path and os.path.exists(icon_path):
                    # 对于CustomTkinter，需要使用iconbitmap方法
                    self.iconbitmap(icon_path)

                    # 同时设置wm_iconbitmap以确保兼容性
                    self.wm_iconbitmap(icon_path)

                    # 强制刷新窗口
                    self.update_idletasks()
                    return

        except Exception:
            # 如果设置图标失败，静默处理，不影响程序运行
            pass

    def maximize_window(self):
        """最大化窗口"""
        try:
            # Windows系统
            if os.name == 'nt':
                self.state('zoomed')
            else:
                # Linux/Unix系统
                try:
                    self.attributes('-zoomed', True)
                except:
                    # 如果-zoomed不支持，尝试其他方法
                    self.geometry("1200x800")
        except Exception:
            # 如果最大化失败，至少设置一个较大的窗口尺寸
            try:
                self.geometry("1200x800")
            except:
                pass

    def create_widgets(self):
        """创建界面组件"""
        # 主容器 - 增加内边距
        main_container = ctk.CTkFrame(self, fg_color="transparent")
        main_container.pack(fill="both", expand=True, padx=15, pady=15)

        # 创建左右分栏 - 直接使用主容器
        content_frame = ctk.CTkFrame(main_container, fg_color="transparent")
        content_frame.pack(fill="both", expand=True)

        # 左侧面板 - 调整宽度比例
        left_panel = ctk.CTkFrame(content_frame)
        left_panel.pack(side="left", fill="both", expand=False, padx=(0, 8))
        left_panel.configure(width=380)  # 固定左侧宽度

        # 右侧面板 - 占用剩余空间
        right_panel = ctk.CTkFrame(content_frame)
        right_panel.pack(side="right", fill="both", expand=True, padx=(8, 0))

        self.create_left_panel(left_panel)
        self.create_right_panel(right_panel)
    
    def create_left_panel(self, parent):
        """创建左侧面板"""
        # 系统信息卡片 - 减少高度
        self.system_info_card = StatusCard(parent, "📋 系统信息")
        self.system_info_card.pack(fill="x", pady=(0, 12))

        # AI助手选择器
        try:
            from .ai_assistant_selector import AIAssistantSelector
            self.ai_selector = AIAssistantSelector(parent)
            self.ai_selector.pack(fill="x", pady=(0, 12))
            self.ai_selector.set_selection_callback(self.on_ai_selection_change)
        except ImportError:
            # 如果导入失败，显示简单的选择信息
            ai_info_frame = ctk.CTkFrame(parent)
            ai_info_frame.pack(fill="x", pady=(0, 12))
            ai_label = ctk.CTkLabel(ai_info_frame, text="🤖 支持8种AI助手", font=("Microsoft YaHei UI", 12))
            ai_label.pack(pady=10)

        # 使用整合的界面布局
        from .integrated_layout import create_integrated_action_panel, create_clean_all_method

        # 创建整合的操作面板
        action_frame = create_integrated_action_panel(parent, self)

        # 替换原有的clean_all方法
        self.clean_all = create_clean_all_method(self)

        # 进度卡片 - 占用剩余空间
        self.progress_card = ProgressCard(parent, "📊 操作进度")
        self.progress_card.pack(fill="both", expand=True, pady=(0, 0))

    def on_ai_selection_change(self, selected_assistants):
        """AI助手选择变化回调"""
        try:
            if hasattr(self, 'ai_selector'):
                summary = self.ai_selector.get_selection_summary()
                self.log_textbox.append_log(f"🤖 {summary}", "INFO")

                # 保存选择
                self.ai_selector.save_selection()

                # 更新按钮文字为通用化
                self.update_button_texts_for_selection(selected_assistants)
        except Exception as e:
            self.log_textbox.append_log(f"AI助手选择更新失败: {str(e)}", "ERROR")

    def update_button_texts_for_selection(self, selected_assistants):
        """根据选择的AI助手更新按钮文字为通用化"""
        try:
            if not selected_assistants:
                return

            # 更新按钮文字为通用化
            if hasattr(self, 'augment_account_btn'):
                if len(selected_assistants) == 1:
                    assistant_name = self.get_assistant_display_name(selected_assistants[0])
                    self.augment_account_btn.configure(text=f"🔄 {assistant_name}账号重置")
                else:
                    self.augment_account_btn.configure(text="🔄 多AI助手账号重置")

            # 更新其他相关按钮
            if hasattr(self, 'device_fingerprint_btn'):
                self.device_fingerprint_btn.configure(text="🔒 设备指纹清理")

            if hasattr(self, 'browser_clean_btn'):
                self.browser_clean_btn.configure(text="🌐 浏览器数据清理")

        except Exception as e:
            self.log_textbox.append_log(f"更新按钮文字失败: {str(e)}", "ERROR")

    def get_assistant_display_name(self, assistant_id):
        """获取AI助手显示名称"""
        assistant_names = {
            'augment_code': 'Augment Code',
            'cursor_ai': 'Cursor AI',
            'github_copilot': 'GitHub Copilot',
            'tabnine': 'Tabnine',
            'codeium': 'Codeium',
            'claude_ai': 'Claude AI',
            'amazon_codewhisperer': 'CodeWhisperer',
            'sourcegraph_cody': 'Sourcegraph Cody'
        }
        return assistant_names.get(assistant_id, assistant_id)

    def reset_selected_ai_assistant(self):
        """重置选择的AI助手 - 通用化方法"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 获取选择的AI助手
        try:
            if hasattr(self, 'ai_selector'):
                selected_assistants = self.ai_selector.get_selected_assistants()
                if not selected_assistants:
                    messagebox.showwarning("提示", "请先选择要重置的AI助手")
                    return
            else:
                # 如果没有选择器，默认为Augment
                selected_assistants = ['augment_code']
        except Exception as e:
            self.log_textbox.append_log(f"获取AI助手选择失败: {str(e)}", "ERROR")
            selected_assistants = ['augment_code']

        # 根据选择的AI助手执行不同的重置策略
        if len(selected_assistants) == 1:
            assistant_id = selected_assistants[0]
            if assistant_id == 'augment_code':
                self._reset_augment_enhanced()
            elif assistant_id == 'cursor_ai':
                self._reset_cursor_enhanced()
            else:
                self._reset_generic_ai_assistant(assistant_id)
        else:
            # 多个AI助手，使用增强重置器
            self._reset_multiple_ai_assistants(selected_assistants)

    def _reset_augment_enhanced(self):
        """增强版Augment重置"""
        # 确认对话框
        confirm_message = "🔄 Augment Code 增强重置功能说明:\n\n"
        confirm_message += "✨ 2025年最新增强功能:\n"
        confirm_message += "• 深度清理所有Augment相关数据\n"
        confirm_message += "• 重置设备指纹和机器标识\n"
        confirm_message += "• 清理浏览器指纹数据\n"
        confirm_message += "• 应用反检测措施\n"
        confirm_message += "• 网络指纹重置\n\n"
        confirm_message += "⚠️ 注意：此操作会清理所有Augment相关数据\n"
        confirm_message += "操作前会自动备份数据。\n\n是否继续？"

        result = messagebox.askyesno("Augment增强重置确认", confirm_message)
        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_augment_reset():
            try:
                from utils.enhanced_ai_assistant_resetter import EnhancedAIAssistantResetter
                resetter = EnhancedAIAssistantResetter()
                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "augment_enhanced_backup")

                # 执行Augment专用重置
                result = resetter._reset_single_assistant('augment_code', resetter.ai_assistants['augment_code'])

                # 应用高级技术
                advanced_result = resetter._apply_advanced_bypass_techniques()
                result['techniques'].extend(advanced_result['techniques'])
                result['errors'].extend(advanced_result['errors'])

                return result
            except Exception as e:
                raise e

        def success_callback(result):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.update_progress(100, "Augment增强重置完成")

            self.log_textbox.append_log("🔄 Augment增强重置完成！", "SUCCESS")
            self.log_textbox.append_log(f"应用了 {len(result['techniques'])} 种技术", "INFO")

            if result['errors']:
                self.log_textbox.append_log("⚠️ 遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            messagebox.showinfo(
                "Augment增强重置完成",
                "🎉 Augment增强重置完成！\n\n" +
                f"✅ 应用技术: {len(result['techniques'])} 种\n" +
                f"⚠️ 遇到错误: {len(result['errors'])} 个\n\n" +
                "现在可以重新启动VSCode并使用Augment！"
            )

        def error_callback(error):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"Augment增强重置失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.augment_account_reset_btn.set_processing(True, "重置中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始Augment增强重置...", "INFO")

        self.current_operation = ThreadedOperation(
            run_augment_reset,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def _reset_cursor_enhanced(self):
        """增强版Cursor重置 - 基于yuaotian/go-cursor-help"""
        # 确认对话框
        confirm_message = "🔄 Cursor AI 增强重置功能说明:\n\n"
        confirm_message += "✨ 基于yuaotian/go-cursor-help项目的最新方法:\n"
        confirm_message += "• 解决'Too many free trial accounts'问题\n"
        confirm_message += "• 机器ID和设备指纹重置\n"
        confirm_message += "• 注册表GUID重新生成\n"
        confirm_message += "• MAC地址修改\n"
        confirm_message += "• 网络适配器重置\n"
        confirm_message += "• 反检测措施应用\n\n"
        confirm_message += "⚠️ 注意：此操作会清理所有Cursor相关数据\n"
        confirm_message += "操作前会自动备份数据。\n\n是否继续？"

        result = messagebox.askyesno("Cursor增强重置确认", confirm_message)
        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_cursor_reset():
            try:
                from utils.enhanced_ai_assistant_resetter import EnhancedAIAssistantResetter
                resetter = EnhancedAIAssistantResetter()

                # 执行Cursor专用增强重置
                return resetter.reset_cursor_ai_enhanced()
            except Exception as e:
                raise e

        def success_callback(result):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.update_progress(100, "Cursor增强重置完成")

            self.log_textbox.append_log("🔄 Cursor增强重置完成！", "SUCCESS")
            self.log_textbox.append_log(f"应用了 {len(result['techniques_applied'])} 种技术", "INFO")

            if result['errors']:
                self.log_textbox.append_log("⚠️ 遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            messagebox.showinfo(
                "Cursor增强重置完成",
                "🎉 Cursor增强重置完成！\n\n" +
                f"✅ 应用技术: {len(result['techniques_applied'])} 种\n" +
                f"⚠️ 遇到错误: {len(result['errors'])} 个\n\n" +
                "🚀 特别功能:\n" +
                "• yuaotian方法已应用\n" +
                "• 机器限制已绕过\n" +
                "• 设备指纹已重置\n\n" +
                "现在可以重新启动Cursor并使用！"
            )

        def error_callback(error):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"Cursor增强重置失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.augment_account_reset_btn.set_processing(True, "重置中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始Cursor增强重置...", "INFO")

        self.current_operation = ThreadedOperation(
            run_cursor_reset,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def _reset_generic_ai_assistant(self, assistant_id):
        """重置通用AI助手"""
        assistant_name = self.get_assistant_display_name(assistant_id)

        # 确认对话框
        confirm_message = f"🔄 {assistant_name} 重置功能说明:\n\n"
        confirm_message += "✨ 重置功能:\n"
        confirm_message += "• 清理扩展存储数据\n"
        confirm_message += "• 重置试用状态\n"
        confirm_message += "• 清理配置文件\n"
        confirm_message += "• 删除认证信息\n\n"
        confirm_message += f"⚠️ 注意：此操作会清理所有{assistant_name}相关数据\n"
        confirm_message += "操作前会自动备份数据。\n\n是否继续？"

        result = messagebox.askyesno(f"{assistant_name}重置确认", confirm_message)
        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_generic_reset():
            try:
                from utils.enhanced_ai_assistant_resetter import EnhancedAIAssistantResetter
                resetter = EnhancedAIAssistantResetter()

                if assistant_id in resetter.ai_assistants:
                    config = resetter.ai_assistants[assistant_id]
                    return resetter._reset_single_assistant(assistant_id, config)
                else:
                    raise ValueError(f"不支持的AI助手: {assistant_id}")
            except Exception as e:
                raise e

        def success_callback(result):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.update_progress(100, f"{assistant_name}重置完成")

            self.log_textbox.append_log(f"🔄 {assistant_name}重置完成！", "SUCCESS")
            self.log_textbox.append_log(f"应用了 {len(result['techniques'])} 种技术", "INFO")

            if result['errors']:
                self.log_textbox.append_log("⚠️ 遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            messagebox.showinfo(
                f"{assistant_name}重置完成",
                f"🎉 {assistant_name}重置完成！\n\n" +
                f"✅ 应用技术: {len(result['techniques'])} 种\n" +
                f"⚠️ 遇到错误: {len(result['errors'])} 个\n\n" +
                f"现在可以重新启动并使用{assistant_name}！"
            )

        def error_callback(error):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"{assistant_name}重置失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.augment_account_reset_btn.set_processing(True, "重置中...")
        self.progress_card.reset()
        self.log_textbox.append_log(f"开始{assistant_name}重置...", "INFO")

        self.current_operation = ThreadedOperation(
            run_generic_reset,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def _reset_multiple_ai_assistants(self, selected_assistants):
        """重置多个AI助手"""
        assistant_names = [self.get_assistant_display_name(aid) for aid in selected_assistants]

        # 确认对话框
        confirm_message = "🔄 多AI助手重置功能说明:\n\n"
        confirm_message += f"✨ 将重置以下 {len(selected_assistants)} 个AI助手:\n"
        for name in assistant_names:
            confirm_message += f"• {name}\n"
        confirm_message += "\n🚀 重置功能:\n"
        confirm_message += "• 清理所有选择的AI助手数据\n"
        confirm_message += "• 重置设备指纹\n"
        confirm_message += "• 应用高级绕过技术\n"
        confirm_message += "• 网络指纹重置\n\n"
        confirm_message += "⚠️ 注意：此操作会清理所有选择的AI助手数据\n"
        confirm_message += "操作前会自动备份数据。\n\n是否继续？"

        result = messagebox.askyesno("多AI助手重置确认", confirm_message)
        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_multiple_reset():
            try:
                from utils.enhanced_ai_assistant_resetter import EnhancedAIAssistantResetter
                resetter = EnhancedAIAssistantResetter()

                # 只重置选择的AI助手
                results = {
                    'success': True,
                    'assistants_reset': [],
                    'errors': [],
                    'techniques_applied': []
                }

                for assistant_id in selected_assistants:
                    if assistant_id in resetter.ai_assistants:
                        config = resetter.ai_assistants[assistant_id]
                        assistant_result = resetter._reset_single_assistant(assistant_id, config)
                        if assistant_result['success']:
                            results['assistants_reset'].append(config['name'])
                            results['techniques_applied'].extend(assistant_result['techniques'])
                        else:
                            results['errors'].extend(assistant_result['errors'])

                # 应用高级绕过技术
                advanced_result = resetter._apply_advanced_bypass_techniques()
                results['techniques_applied'].extend(advanced_result['techniques'])
                results['errors'].extend(advanced_result['errors'])

                if results['errors']:
                    results['success'] = False

                return results
            except Exception as e:
                raise e

        def success_callback(result):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.update_progress(100, "多AI助手重置完成")

            self.log_textbox.append_log("🔄 多AI助手重置完成！", "SUCCESS")
            self.log_textbox.append_log(f"重置了 {len(result['assistants_reset'])} 个AI助手", "INFO")
            self.log_textbox.append_log(f"应用了 {len(result['techniques_applied'])} 种技术", "INFO")

            if result['errors']:
                self.log_textbox.append_log("⚠️ 遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            messagebox.showinfo(
                "多AI助手重置完成",
                "🎉 多AI助手重置完成！\n\n" +
                f"✅ 重置AI助手: {len(result['assistants_reset'])} 个\n" +
                f"✅ 应用技术: {len(result['techniques_applied'])} 种\n" +
                f"⚠️ 遇到错误: {len(result['errors'])} 个\n\n" +
                "现在可以重新启动并使用所有AI助手！"
            )

        def error_callback(error):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"多AI助手重置失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.augment_account_reset_btn.set_processing(True, "重置中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始多AI助手重置...", "INFO")

        self.current_operation = ThreadedOperation(
            run_multiple_reset,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def create_right_panel(self, parent):
        """创建右侧面板"""
        # 日志区域标题
        log_title = ctk.CTkLabel(
            parent,
            text="📝 操作日志",
            font=FONTS['subtitle'],
            text_color=COLORS['text_primary']
        )
        log_title.pack(pady=(12, 8))

        # 日志文本框 - 恢复原始大小，占用大部分空间
        self.log_textbox = LogTextBox(parent)
        self.log_textbox.pack(fill="both", expand=True, padx=15, pady=(0, 8))

        # 日志控制区域 - 包含所有按钮
        log_control_frame = ctk.CTkFrame(parent, fg_color="transparent")
        log_control_frame.pack(fill="x", padx=15, pady=(0, 12))

        # 左侧：使用说明
        info_label = ctk.CTkLabel(
            log_control_frame,
            text="💡 使用前请确保已完全退出 VS Code",
            font=FONTS['small'],
            text_color=COLORS['warning']
        )
        info_label.pack(side="left")

        # 右侧按钮组
        button_group = ctk.CTkFrame(log_control_frame, fg_color="transparent")
        button_group.pack(side="right")

        # 查看源码按钮
        source_btn = ActionButton(
            button_group,
            text="🔗 查看源码",
            command=self.open_source_code,
            style="primary"
        )
        source_btn.pack(side="right", padx=(8, 0))

        # 更新按钮 - 显示版本信息
        self.update_btn = ActionButton(
            button_group,
            text=f"🔄 v{get_current_version()}",
            command=self.check_for_updates_manual,
            style="warning"
        )
        self.update_btn.pack(side="right", padx=(8, 0))
        self.update_btn.configure(state="disabled")  # 初始状态为禁用

        # 清空日志按钮
        clear_log_btn = ActionButton(
            button_group,
            text="🗑 清空日志",
            command=self.clear_log,
            style="danger"
        )
        clear_log_btn.pack(side="right", padx=(8, 0))

        # 添加欢迎消息
        self.log_textbox.append_log("🆓 欢迎使用 AugmentNew 免费版！", "SUCCESS")
        self.log_textbox.append_log("💯 本软件永久免费，无需激活码，拒绝任何收费！", "SUCCESS")
        self.log_textbox.append_log("🔓 完全开源，源码透明，安全可靠", "INFO")
        self.log_textbox.append_log("选择需要的操作，程序会自动创建备份文件", "INFO")



    def open_source_code(self):
        """打开源码链接"""
        import webbrowser
        try:
            webbrowser.open("https://github.com/alltobebetter/AugmentNew")
            self.log_textbox.append_log("已在浏览器中打开源码页面", "INFO")
        except Exception as e:
            self.log_textbox.append_log(f"打开源码页面失败: {str(e)}", "ERROR")

    def check_for_updates_async(self):
        """异步检查更新"""
        def check_updates():
            try:
                result = check_for_updates()
                # 在主线程中更新UI
                self.after(0, lambda: self.update_version_status(result))
            except Exception as e:
                self.after(0, lambda: self.log_textbox.append_log(f"检查更新失败: {str(e)}", "ERROR"))

        # 在后台线程中检查更新
        import threading
        thread = threading.Thread(target=check_updates, daemon=True)
        thread.start()

    def check_for_updates_manual(self):
        """手动检查更新"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        self.update_btn.set_processing(True, "检查中...")
        self.log_textbox.append_log("正在检查更新...", "INFO")

        def check_updates():
            try:
                result = check_for_updates()
                self.after(0, lambda: self.handle_manual_update_check(result))
            except Exception as e:
                self.after(0, lambda: self.handle_update_check_error(str(e)))

        import threading
        thread = threading.Thread(target=check_updates, daemon=True)
        thread.start()

    def update_version_status(self, result):
        """更新版本状态"""
        if result['error']:
            # 检查失败，保持按钮为检查更新状态
            self.update_btn.configure(
                text=f"🔄 v{get_current_version()}",
                state="normal"
            )
            return

        self.latest_version = result['latest_version']
        self.update_available = result['has_update']

        if result['has_update']:
            # 有更新可用
            self.update_btn.configure(
                text=f"🆕 v{result['latest_version']} 可用",
                state="normal",
                command=self.open_update_page
            )
            # 更新按钮样式为成功色
            self.update_btn.configure(fg_color=COLORS['success'])
        else:
            # 已是最新版本
            self.update_btn.configure(
                text=f"✅ v{get_current_version()} 最新",
                state="disabled"
            )

    def handle_manual_update_check(self, result):
        """处理手动更新检查结果"""
        self.update_btn.set_processing(False)

        if result['error']:
            self.log_textbox.append_log(f"检查更新失败: {result['error']}", "ERROR")
            messagebox.showerror("检查更新失败", result['error'])
            return

        if result['has_update']:
            self.log_textbox.append_log(f"发现新版本: v{result['latest_version']}", "SUCCESS")
            messagebox.showinfo(
                "发现新版本",
                f"发现新版本 v{result['latest_version']}！\n\n"
                f"当前版本: v{get_current_version()}\n"
                f"最新版本: v{result['latest_version']}\n\n"
                "点击更新按钮访问下载页面"
            )
        else:
            self.log_textbox.append_log("已是最新版本", "INFO")
            messagebox.showinfo("检查更新", "您使用的已是最新版本！")

        self.update_version_status(result)

    def handle_update_check_error(self, error):
        """处理更新检查错误"""
        self.update_btn.set_processing(False)
        self.log_textbox.append_log(f"检查更新失败: {error}", "ERROR")

    def open_update_page(self):
        """打开更新页面"""
        import webbrowser
        try:
            webbrowser.open(get_update_url())
            self.log_textbox.append_log("已在浏览器中打开更新页面", "INFO")
        except Exception as e:
            self.log_textbox.append_log(f"打开更新页面失败: {str(e)}", "ERROR")

    def update_system_info(self):
        """更新系统信息"""
        try:
            # 清空现有信息
            for widget in self.system_info_card.content_frame.winfo_children():
                widget.destroy()
            
            # 添加系统路径信息
            paths_info = [
                ("主目录", get_home_dir()),
                ("应用数据目录", get_app_data_dir()),
                ("存储文件", get_storage_path()),
                ("数据库文件", get_db_path()),
                ("机器ID文件", get_machine_id_path()),
                ("工作区存储", get_workspace_storage_path())
            ]
            
            for label, path in paths_info:
                exists = os.path.exists(path)
                color = COLORS['success'] if exists else COLORS['danger']
                status = "✅" if exists else "❌"
                
                self.system_info_card.add_info_row(
                    label, 
                    f"{status} {os.path.basename(path)}", 
                    color
                )
            
            self.log_textbox.append_log("系统信息已更新", "INFO")
            
        except Exception as e:
            self.log_textbox.append_log(f"更新系统信息失败: {str(e)}", "ERROR")
    
    def modify_telemetry_ids(self):
        """修改Telemetry ID"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return
        
        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)
        
        def success_callback(result):
            self.modify_ids_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "修改完成")
            
            self.log_textbox.append_log("Telemetry ID 修改成功！", "SUCCESS")
            self.log_textbox.append_log(f"存储备份: {result['storage_backup_path']}", "INFO")
            if result['machine_id_backup_path']:
                self.log_textbox.append_log(f"机器ID备份: {result['machine_id_backup_path']}", "INFO")
            
            self.log_textbox.append_log(f"旧机器ID: {result['old_machine_id'][:16]}...", "INFO")
            self.log_textbox.append_log(f"新机器ID: {result['new_machine_id'][:16]}...", "INFO")
            self.log_textbox.append_log(f"旧设备ID: {result['old_device_id']}", "INFO")
            self.log_textbox.append_log(f"新设备ID: {result['new_device_id']}", "INFO")
        
        def error_callback(error):
            self.modify_ids_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"修改Telemetry ID失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")
        
        self.modify_ids_btn.set_processing(True, "修改中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始修改 Telemetry ID...", "INFO")
        
        self.current_operation = ThreadedOperation(
            modify_telemetry_ids,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def clean_database(self):
        """清理数据库"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def success_callback(result):
            self.clean_db_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "清理完成")

            self.log_textbox.append_log("数据库清理成功！", "SUCCESS")
            self.log_textbox.append_log(f"数据库备份: {result['db_backup_path']}", "INFO")
            self.log_textbox.append_log(f"删除了 {result['deleted_rows']} 条包含'augment'的记录", "INFO")

        def error_callback(error):
            self.clean_db_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"清理数据库失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.clean_db_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始清理数据库...", "INFO")

        self.current_operation = ThreadedOperation(
            clean_augment_data,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def clean_workspace(self):
        """清理工作区"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def success_callback(result):
            self.clean_workspace_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "清理完成")

            self.log_textbox.append_log("工作区清理成功！", "SUCCESS")
            self.log_textbox.append_log(f"工作区备份: {result['backup_path']}", "INFO")
            self.log_textbox.append_log(f"删除了 {result['deleted_files_count']} 个文件", "INFO")

            if result.get('failed_operations'):
                self.log_textbox.append_log(f"有 {len(result['failed_operations'])} 个操作失败", "WARNING")

            if result.get('failed_compressions'):
                self.log_textbox.append_log(f"有 {len(result['failed_compressions'])} 个文件备份失败", "WARNING")

        def error_callback(error):
            self.clean_workspace_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"清理工作区失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.clean_workspace_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始清理工作区...", "INFO")

        self.current_operation = ThreadedOperation(
            clean_workspace_storage,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def clean_all(self):
        """一键清理全部 - 2.0版本支持IDE选择"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 首先显示IDE选择对话框
        try:
            from .ide_selector_dialog import IDESelectorDialog
            from utils.augment_account_resetter import AugmentAccountResetter

            # 创建临时重置器来检测可用的IDE
            temp_resetter = AugmentAccountResetter("auto")
            available_ides = temp_resetter.get_detected_ides()

            # 显示IDE选择对话框
            dialog = IDESelectorDialog(self, available_ides)
            self.wait_window(dialog)

            selected_ide = dialog.get_result()
            if not selected_ide:
                return  # 用户取消了选择

        except Exception as e:
            messagebox.showerror("错误", f"IDE选择失败: {str(e)}")
            return

        # 根据选择的IDE显示确认对话框
        ide_type_names = {
            'vscode': 'VSCode + Augment插件',
            'cursor': 'Cursor IDE',
            'auto': '自动检测的所有IDE'
        }
        selected_name = ide_type_names.get(selected_ide, selected_ide)

        confirm_message = f"🔧 一键清理全部 - {selected_name}\n\n"
        confirm_message += "这将执行以下操作：\n\n"

        if selected_ide in ['vscode', 'auto'] and available_ides.get('vscode', False):
            confirm_message += "📝 VSCode相关:\n"
            confirm_message += "• 修改 VSCode Telemetry ID\n"
            confirm_message += "• 清理 VSCode 数据库\n"
            confirm_message += "• 清理 VSCode 工作区存储\n"
            confirm_message += "• 清理 Augment 插件数据\n\n"

        if selected_ide in ['cursor', 'auto'] and available_ides.get('cursor', False):
            confirm_message += "🎯 Cursor相关:\n"
            confirm_message += "• 修改 Cursor 机器ID\n"
            confirm_message += "• 清理 Cursor 存储数据\n"
            confirm_message += "• 清理 Cursor 缓存数据\n"
            confirm_message += "• 清理 AI助手试用状态\n\n"

        confirm_message += "所有操作都会自动创建备份。\n"
        confirm_message += "确定要继续吗？"

        result = messagebox.askyesno("确认操作", confirm_message)
        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_all_operations():
            """运行所有清理操作 - 2.0版本支持IDE区分"""
            try:
                progress_callback(0.1, f"开始一键清理 - {selected_name}...")
                results = {}

                # 根据选择的IDE执行相应的清理
                if selected_ide in ['vscode', 'auto'] and available_ides.get('vscode', False):
                    progress_callback(0.2, "清理VSCode Telemetry ID...")
                    telemetry_result = modify_telemetry_ids()
                    results['telemetry'] = telemetry_result

                    progress_callback(0.4, "清理VSCode数据库...")
                    db_result = clean_augment_data()
                    results['database'] = db_result

                    progress_callback(0.6, "清理VSCode工作区...")
                    ws_result = clean_workspace_storage()
                    results['workspace'] = ws_result

                if selected_ide in ['cursor', 'auto'] and available_ides.get('cursor', False):
                    progress_callback(0.7, "清理Cursor存储数据...")
                    from utils.cursor_ai_resetter import CursorAIResetter
                    cursor_resetter = CursorAIResetter()

                    cursor_storage_result = cursor_resetter._clean_cursor_storage_data()
                    results['cursor_storage'] = cursor_storage_result

                    progress_callback(0.8, "重置Cursor机器ID...")
                    cursor_machine_result = cursor_resetter._reset_cursor_machine_id()
                    results['cursor_machine_id'] = cursor_machine_result

                # 通用清理（浏览器数据等）
                progress_callback(0.9, "清理浏览器数据...")
                from utils.augment_account_resetter import AugmentAccountResetter
                resetter = AugmentAccountResetter(selected_ide)
                browser_result = resetter._reset_browser_augment_data()
                results['browser_cleanup'] = browser_result

                progress_callback(1.0, "全部操作完成")

                # 确保兼容性：如果没有VSCode结果，创建默认结果
                if 'telemetry' not in results:
                    results['telemetry'] = {'success': True, 'message': '未执行VSCode清理'}
                if 'database' not in results:
                    results['database'] = {'deleted_rows': 0, 'success': True}
                if 'workspace' not in results:
                    results['workspace'] = {'deleted_files_count': 0, 'success': True}

                return results

            except Exception as e:
                raise e

        def success_callback(results):
            self.clean_all_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "全部完成")

            self.log_textbox.append_log(f"🎉 一键清理全部完成 - {selected_name}！", "SUCCESS")

            self.log_textbox.append_log("=" * 50, "INFO")
            self.log_textbox.append_log("📋 操作摘要:", "INFO")

            # 显示VSCode相关结果
            if 'telemetry' in results and results['telemetry'].get('success', False):
                self.log_textbox.append_log(f"✅ VSCode Telemetry ID已更新", "SUCCESS")
            if 'database' in results and results['database'].get('success', False):
                deleted_rows = results['database'].get('deleted_rows', 0)
                self.log_textbox.append_log(f"✅ VSCode数据库清理完成，删除 {deleted_rows} 条记录", "SUCCESS")
            if 'workspace' in results and results['workspace'].get('success', False):
                deleted_files = results['workspace'].get('deleted_files_count', 0)
                self.log_textbox.append_log(f"✅ VSCode工作区清理完成，删除 {deleted_files} 个文件", "SUCCESS")

            # 显示Cursor相关结果
            if 'cursor_storage' in results:
                cursor_storage = results['cursor_storage']
                if cursor_storage.get('success', False):
                    reset_items = len(cursor_storage.get('reset_items', []))
                    self.log_textbox.append_log(f"✅ Cursor存储数据清理完成，重置 {reset_items} 项", "SUCCESS")

            if 'cursor_machine_id' in results:
                cursor_machine = results['cursor_machine_id']
                if cursor_machine.get('success', False):
                    reset_items = len(cursor_machine.get('reset_items', []))
                    self.log_textbox.append_log(f"✅ Cursor机器ID重置完成，更新 {reset_items} 项", "SUCCESS")

            # 显示浏览器清理结果
            if 'browser_cleanup' in results:
                browser_cleanup = results['browser_cleanup']
                if browser_cleanup.get('success', False):
                    reset_items = len(browser_cleanup.get('reset_items', []))
                    self.log_textbox.append_log(f"✅ 浏览器数据清理完成，清理 {reset_items} 项", "SUCCESS")

            self.log_textbox.append_log("=" * 50, "INFO")

            # 根据清理的IDE显示相应的重启提示
            restart_message = "一键清理已完成！\n\n"
            if selected_ide in ['vscode', 'auto'] and available_ides.get('vscode', False):
                restart_message += "• 请重新启动 VSCode 并使用新邮箱登录 Augment 插件\n"
            if selected_ide in ['cursor', 'auto'] and available_ides.get('cursor', False):
                restart_message += "• 请重新启动 Cursor 并重新登录 AI 助手\n"
            restart_message += "\n现在可以享受全新的AI助手体验了！"

            messagebox.showinfo("操作完成", restart_message)

        def error_callback(error):
            self.clean_all_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"一键清理失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.clean_all_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始一键清理全部操作...", "INFO")

        self.current_operation = ThreadedOperation(
            run_all_operations,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def delete_all_backups(self):
        """删除所有备份文件"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 首先查找备份文件
        try:
            backup_files = find_backup_files()
            total_files = sum(len(files) for files in backup_files.values())

            if total_files == 0:
                messagebox.showinfo("提示", "没有找到备份文件")
                self.log_textbox.append_log("没有找到备份文件", "INFO")
                return

            # 确认对话框
            result = messagebox.askyesno(
                "确认删除",
                f"找到 {total_files} 个备份文件：\n\n"
                f"• 存储配置备份: {len(backup_files['storage_backups'])} 个\n"
                f"• 数据库备份: {len(backup_files['db_backups'])} 个\n"
                f"• 机器ID备份: {len(backup_files['machine_id_backups'])} 个\n"
                f"• 工作区备份: {len(backup_files['workspace_backups'])} 个\n\n"
                "确定要删除所有备份文件吗？\n"
                "此操作不可撤销！"
            )

            if not result:
                return

        except Exception as e:
            messagebox.showerror("错误", f"查找备份文件失败: {str(e)}")
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def success_callback(result):
            self.delete_backups_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "删除完成")

            self.log_textbox.append_log("备份文件删除成功！", "SUCCESS")
            self.log_textbox.append_log(f"删除了 {result['deleted_count']} 个文件", "INFO")
            self.log_textbox.append_log(f"释放空间: {result['total_size_freed']:.2f} MB", "INFO")

            if result['failed_files']:
                self.log_textbox.append_log(f"有 {len(result['failed_files'])} 个文件删除失败", "WARNING")
                for failed_file in result['failed_files']:
                    self.log_textbox.append_log(f"失败: {failed_file}", "WARNING")

            # 显示备份类型统计
            backup_types = result.get('backup_types', {})
            if backup_types:
                self.log_textbox.append_log("删除的备份类型:", "INFO")
                for backup_type, count in backup_types.items():
                    if count > 0:
                        type_name = {
                            'storage_backups': '存储配置备份',
                            'db_backups': '数据库备份',
                            'machine_id_backups': '机器ID备份',
                            'workspace_backups': '工作区备份'
                        }.get(backup_type, backup_type)
                        self.log_textbox.append_log(f"  • {type_name}: {count} 个", "INFO")

        def error_callback(error):
            self.delete_backups_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"删除备份文件失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.delete_backups_btn.set_processing(True, "删除中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始删除备份文件...", "INFO")

        self.current_operation = ThreadedOperation(
            clean_all_backups,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def clear_log(self):
        """清空日志"""
        self.log_textbox.clear_log()
        self.log_textbox.append_log("日志已清空", "INFO")

    def advanced_clean(self):
        """高级清理 - 解决注册限制"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 显示警告对话框
        result = messagebox.askyesno(
            "高级清理确认",
            "高级清理将执行以下操作：\n\n"
            "• 清理VS Code注册表项\n"
            "• 清理系统临时文件\n"
            "• 重置网络配置\n"
            "• 清理浏览器数据\n"
            "• 修改系统标识符\n\n"
            "这些操作可以解决频繁注册被限制的问题。\n"
            "操作前会自动创建备份。\n\n"
            "是否继续？"
        )

        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def success_callback(result):
            self.advanced_clean_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "高级清理完成")

            self.log_textbox.append_log("🚀 高级清理完成！", "SUCCESS")
            if result['backup_path']:
                self.log_textbox.append_log(f"📦 系统备份: {result['backup_path']}", "INFO")

            self.log_textbox.append_log("执行的操作:", "INFO")
            for op in result['operations']:
                self.log_textbox.append_log(f"  ✅ {op}", "SUCCESS")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            # 显示注册建议
            self.show_registration_tips()

        def error_callback(error):
            self.advanced_clean_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"高级清理失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        def run_advanced_clean():
            try:
                cleaner = AdvancedCleaner()
                return cleaner.deep_system_reset()
            except Exception as e:
                raise e

        self.advanced_clean_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始高级清理...", "INFO")

        self.current_operation = ThreadedOperation(
            run_advanced_clean,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def show_registration_tips(self):
        """显示注册建议"""
        try:
            cleaner = AdvancedCleaner()
            strategy = cleaner.generate_registration_strategy()

            tips_text = "📋 注册策略建议：\n\n"
            tips_text += f"⏰ 建议等待时间: {strategy['timing']['wait_time']} 秒\n"
            tips_text += f"🕐 最佳注册时间: {strategy['timing']['best_hours']}\n\n"
            tips_text += "💡 注册技巧:\n"
            for tip in strategy['tips']:
                tips_text += f"  • {tip}\n"

            messagebox.showinfo("注册建议", tips_text)

        except Exception as e:
            self.log_textbox.append_log(f"获取注册建议失败: {str(e)}", "ERROR")

    def show_recovery_dialog(self):
        """显示数据恢复对话框"""
        try:
            recovery = DataRecovery()

            # 扫描备份文件
            backups = recovery.scan_backup_files()
            restore_points = recovery.list_restore_points()

            if not backups and not restore_points:
                messagebox.showinfo("数据恢复", "未找到可恢复的备份文件或还原点")
                return

            # 创建恢复选择对话框
            self.create_recovery_dialog(backups, restore_points)

        except Exception as e:
            messagebox.showerror("错误", f"打开恢复对话框失败: {str(e)}")

    def clean_all_browsers(self):
        """多浏览器清理"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 检测已安装的浏览器并获取清理预览
        try:
            browser_cleaner = BrowserCleaner()
            installed_browsers = browser_cleaner.detect_installed_browsers()

            if not installed_browsers:
                messagebox.showinfo("提示", "未检测到支持的浏览器")
                return

            # 获取清理预览
            preview = browser_cleaner.get_cleanup_preview()

            browser_names = {
                'chrome': 'Google Chrome',
                'edge': 'Microsoft Edge',
                'firefox': 'Mozilla Firefox',
                'opera': 'Opera',
                'brave': 'Brave Browser'
            }

            browser_list = [browser_names.get(b, b) for b in installed_browsers]

            # 构建详细的确认信息
            confirm_message = f"检测到以下浏览器：\n\n"
            confirm_message += "\n".join([f"• {name}" for name in browser_list])
            confirm_message += f"\n\n预计清理项目：\n"

            if preview['total_estimated_items'] > 0:
                for browser, items in preview['cleanup_items'].items():
                    browser_name = browser_names.get(browser, browser)
                    total_items = sum(items.values())
                    if total_items > 0:
                        confirm_message += f"• {browser_name}: {total_items} 项\n"
                        if items['local_storage'] > 0:
                            confirm_message += f"  - Local Storage: {items['local_storage']} 项\n"
                        if items['cookies'] > 0:
                            confirm_message += f"  - Cookies: {items['cookies']} 项\n"
                        if items['cache_files'] > 0:
                            confirm_message += f"  - 缓存文件: {items['cache_files']} 项\n"
                        if items['session_storage'] > 0:
                            confirm_message += f"  - Session Storage: {items['session_storage']} 项\n"
                        if items.get('history', 0) > 0:
                            confirm_message += f"  - 历史记录: {items['history']} 项\n"
                        if items.get('bookmarks', 0) > 0:
                            confirm_message += f"  - 书签: {items['bookmarks']} 项\n"

                confirm_message += f"\n总计: {preview['total_estimated_items']} 项Augment相关数据"
            else:
                confirm_message += "• 未检测到Augment相关数据"

            confirm_message += "\n\n⚠️ 注意：只会清理Augment相关的数据，不会影响其他网站数据"
            confirm_message += "\n操作前会自动备份数据。\n\n是否继续？"

            # 显示警告信息
            if preview['warnings']:
                warning_msg = "检测到以下情况：\n" + "\n".join([f"• {w}" for w in preview['warnings']])
                messagebox.showwarning("注意", warning_msg)

            # 确认对话框
            result = messagebox.askyesno("多浏览器清理确认", confirm_message)

            if not result:
                return

        except Exception as e:
            messagebox.showerror("错误", f"检测浏览器失败: {str(e)}")
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_browser_clean():
            try:
                browser_cleaner = BrowserCleaner()
                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "browser_backup")

                # 使用增强的清理方法
                return browser_cleaner.enhanced_clear_all_browsers(
                    backup_dir=backup_dir,
                    progress_callback=progress_callback
                )
            except Exception as e:
                raise e

        def success_callback(result):
            self.browser_clean_btn.set_processing(False)
            self.progress_card.update_progress(100, "浏览器清理完成")

            self.log_textbox.append_log("🌐 多浏览器清理完成！", "SUCCESS")
            self.log_textbox.append_log(f"处理了 {len(result['browsers_processed'])} 个浏览器", "INFO")
            self.log_textbox.append_log(f"总共清理了 {result['total_cleared']} 项数据", "INFO")

            # 显示清理摘要
            summary = result['summary']
            if any(summary.values()):
                self.log_textbox.append_log("📊 清理摘要:", "INFO")
                if summary['local_storage'] > 0:
                    self.log_textbox.append_log(f"  • Local Storage: {summary['local_storage']} 项", "INFO")
                if summary['cookies'] > 0:
                    self.log_textbox.append_log(f"  • Cookies: {summary['cookies']} 项", "INFO")
                if summary['cache_files'] > 0:
                    self.log_textbox.append_log(f"  • 缓存文件: {summary['cache_files']} 项", "INFO")
                if summary['session_storage'] > 0:
                    self.log_textbox.append_log(f"  • Session Storage: {summary['session_storage']} 项", "INFO")
                if summary['history'] > 0:
                    self.log_textbox.append_log(f"  • 历史记录: {summary['history']} 项", "INFO")
                if summary['bookmarks'] > 0:
                    self.log_textbox.append_log(f"  • 书签: {summary['bookmarks']} 项", "INFO")

            # 显示详细结果
            for browser_result in result['browsers_processed']:
                browser = browser_result['browser']
                if browser_result['success']:
                    self.log_textbox.append_log(f"✅ {browser}: 清理成功", "SUCCESS")
                    for item in browser_result['cleared_items']:
                        self.log_textbox.append_log(f"  • {item}", "INFO")
                else:
                    self.log_textbox.append_log(f"❌ {browser}: 清理失败", "ERROR")
                    for error in browser_result['errors']:
                        self.log_textbox.append_log(f"  • {error}", "ERROR")

            # 显示备份信息
            if result['backup_created']:
                self.log_textbox.append_log("💾 数据已备份，可在需要时恢复", "INFO")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

        def error_callback(error):
            self.browser_clean_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"多浏览器清理失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.browser_clean_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始多浏览器清理...", "INFO")

        self.current_operation = ThreadedOperation(
            run_browser_clean,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def fix_storage_error(self):
        """修复Storage错误"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 确认对话框
        result = messagebox.askyesno(
            "Storage错误修复确认",
            "此功能将修复VSCode Storage相关错误：\n\n" +
            "• 创建缺失的目录\n• 重建storage.json文件\n• 修复文件权限\n• 生成新的设备标识\n\n" +
            "操作前会自动备份现有数据。\n是否继续？"
        )

        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_storage_fix():
            try:
                storage_fixer = StorageFixer()
                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "storage_fix_backup")
                return storage_fixer.comprehensive_storage_fix(backup_dir)
            except Exception as e:
                raise e

        def success_callback(result):
            self.storage_fix_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "Storage修复完成")

            self.log_textbox.append_log("🔧 Storage错误修复完成！", "SUCCESS")

            for step in result['steps_completed']:
                self.log_textbox.append_log(f"✅ {step}", "SUCCESS")

            if result['backup_result'] and result['backup_result']['success']:
                self.log_textbox.append_log("📦 数据备份完成", "INFO")
                for backup_file in result['backup_result']['backed_up_files']:
                    self.log_textbox.append_log(f"  • {os.path.basename(backup_file)}", "INFO")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            messagebox.showinfo(
                "修复完成",
                "Storage错误修复完成！\n\n" +
                "现在可以重新启动VSCode，Storage错误应该已经解决。"
            )

        def error_callback(error):
            self.storage_fix_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"Storage修复失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.storage_fix_btn.set_processing(True, "修复中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始Storage错误修复...", "INFO")

        self.current_operation = ThreadedOperation(
            run_storage_fix,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def github_enhanced_clean(self):
        """GitHub增强清理"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 检测GitHub账号信息
        try:
            github_cleaner = GitHubEnhancedCleaner()
            accounts = github_cleaner.detect_github_accounts()

            # 构建账号信息显示
            account_info = []
            if accounts['git_config'].get('name'):
                account_info.append(f"Git用户: {accounts['git_config']['name']}")
            if accounts['git_config'].get('email'):
                account_info.append(f"Git邮箱: {accounts['git_config']['email']}")
            if accounts['ssh_keys']:
                account_info.append(f"SSH密钥: {len(accounts['ssh_keys'])} 个")
            if accounts['stored_credentials']:
                account_info.append(f"存储凭据: {len(accounts['stored_credentials'])} 个")

            account_display = "\n".join(account_info) if account_info else "未检测到GitHub配置"

        except Exception as e:
            messagebox.showerror("错误", f"检测GitHub配置失败: {str(e)}")
            return

        # 获取新的用户信息
        from tkinter import simpledialog

        new_name = simpledialog.askstring(
            "GitHub重置",
            "请输入新的Git用户名:",
            initialvalue="NewUser"
        )
        if not new_name:
            return

        new_email = simpledialog.askstring(
            "GitHub重置",
            "请输入新的Git邮箱:",
            initialvalue="<EMAIL>"
        )
        if not new_email:
            return

        # 确认对话框
        result = messagebox.askyesno(
            "GitHub增强清理确认",
            f"当前GitHub配置：\n{account_display}\n\n" +
            f"将重置为：\n用户名: {new_name}\n邮箱: {new_email}\n\n" +
            "此操作将：\n• 清理GitHub认证信息\n• 生成新的SSH密钥\n• 重置Git配置\n• 清理VSCode Git缓存\n\n" +
            "操作前会自动备份配置。\n是否继续？"
        )

        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_github_clean():
            try:
                github_cleaner = GitHubEnhancedCleaner()
                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "github_backup")
                return github_cleaner.comprehensive_github_reset(backup_dir, new_name, new_email)
            except Exception as e:
                raise e

        def success_callback(result):
            self.github_enhanced_btn.set_processing(False)
            self.progress_card.update_progress(1.0, "GitHub清理完成")

            self.log_textbox.append_log("🐙 GitHub增强清理完成！", "SUCCESS")

            for step in result['steps_completed']:
                self.log_textbox.append_log(f"✅ {step}", "SUCCESS")

            if result['backup_result'] and result['backup_result']['success']:
                self.log_textbox.append_log("📦 GitHub配置备份完成", "INFO")

            if result['ssh_result'] and result['ssh_result']['success']:
                self.log_textbox.append_log("🔑 新SSH密钥已生成", "SUCCESS")
                self.log_textbox.append_log(f"密钥路径: {result['ssh_result']['key_path']}", "INFO")

                # 显示公钥内容供用户复制
                public_key = result['ssh_result']['public_key']
                self.log_textbox.append_log("📋 请复制以下公钥到GitHub:", "INFO")
                self.log_textbox.append_log(public_key, "INFO")

            if result['config_result'] and result['config_result']['success']:
                self.log_textbox.append_log(f"⚙️ Git配置已更新: {new_name} <{new_email}>", "SUCCESS")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            # 显示SSH公钥对话框
            if result['ssh_result'] and result['ssh_result']['success']:
                messagebox.showinfo(
                    "SSH密钥已生成",
                    f"新的SSH密钥已生成！\n\n" +
                    f"请将以下公钥添加到GitHub账户：\n\n" +
                    f"{result['ssh_result']['public_key']}\n\n" +
                    f"密钥已保存到: {result['ssh_result']['key_path']}"
                )

        def error_callback(error):
            self.github_enhanced_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"GitHub增强清理失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.github_enhanced_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始GitHub增强清理...", "INFO")

        self.current_operation = ThreadedOperation(
            run_github_clean,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def device_fingerprint_clean(self):
        """设备指纹清理 - 解决账号锁定问题"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 获取设备指纹分析
        try:
            fingerprint_cleaner = DeviceFingerprintCleaner()
            analysis = fingerprint_cleaner.get_fingerprint_analysis()

            risk_level_text = {
                'high': '高风险',
                'medium': '中等风险',
                'low': '低风险',
                'minimal': '极低风险',
                'unknown': '未知'
            }

            risk_text = risk_level_text.get(analysis['risk_level'], '未知')

            # 构建分析信息
            analysis_info = f"当前设备指纹风险等级: {risk_text}\n\n"
            analysis_info += "检测到的指纹组件:\n"

            if analysis['browser_fingerprint'].get('canvas_data_exists'):
                analysis_info += "• Canvas指纹数据\n"
            if analysis['browser_fingerprint'].get('webgl_data_exists'):
                analysis_info += "• WebGL指纹数据\n"
            if analysis['browser_fingerprint'].get('storage_data_exists'):
                analysis_info += "• 浏览器存储指纹\n"
            if analysis['system_fingerprint'].get('machine_id_exists'):
                analysis_info += "• 机器ID标识\n"
            if analysis['system_fingerprint'].get('device_id_exists'):
                analysis_info += "• 设备ID标识\n"
            if analysis['system_fingerprint'].get('vscode_data_exists'):
                analysis_info += "• VSCode配置数据\n"

        except Exception as e:
            messagebox.showerror("错误", f"设备指纹分析失败: {str(e)}")
            return

        # 确认对话框
        confirm_message = f"{analysis_info}\n"
        confirm_message += "此功能将执行全面的设备指纹重置：\n\n"
        confirm_message += "🔄 浏览器指纹重置:\n"
        confirm_message += "• 清理Canvas指纹数据\n"
        confirm_message += "• 清理WebGL指纹数据\n"
        confirm_message += "• 清理Audio指纹数据\n"
        confirm_message += "• 重置浏览器设备存储\n"
        confirm_message += "• 清理字体指纹数据\n\n"
        confirm_message += "🖥️ 系统指纹重置:\n"
        confirm_message += "• 生成新的机器ID\n"
        confirm_message += "• 生成新的设备ID\n"
        confirm_message += "• 重置VSCode标识符\n"
        confirm_message += "• 清理系统临时指纹\n\n"
        confirm_message += "🌐 网络指纹重置:\n"
        confirm_message += "• 清理DNS缓存\n"
        confirm_message += "• 重置网络适配器配置\n"
        confirm_message += "• 清理ARP缓存\n"
        confirm_message += "• 重置TCP/IP配置\n\n"
        confirm_message += "⚠️ 注意：此操作将彻底改变设备指纹，有助于解决账号锁定问题\n"
        confirm_message += "操作前会自动备份数据。\n\n是否继续？"

        result = messagebox.askyesno("设备指纹清理确认", confirm_message)

        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_fingerprint_clean():
            try:
                fingerprint_cleaner = DeviceFingerprintCleaner()
                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "device_fingerprint_backup")
                return fingerprint_cleaner.comprehensive_fingerprint_reset(backup_dir)
            except Exception as e:
                raise e

        def success_callback(result):
            self.device_fingerprint_btn.set_processing(False)
            self.progress_card.update_progress(100, "设备指纹清理完成")

            self.log_textbox.append_log("🔒 设备指纹清理完成！", "SUCCESS")
            self.log_textbox.append_log(f"重置了 {len(result['components_reset'])} 个指纹组件", "INFO")

            # 显示清理摘要
            summary = result['reset_summary']
            if any(summary.values()):
                self.log_textbox.append_log("📊 清理摘要:", "INFO")
                if summary['browser_fingerprint'] > 0:
                    self.log_textbox.append_log(f"  • 浏览器指纹: {summary['browser_fingerprint']} 项", "INFO")
                if summary['system_fingerprint'] > 0:
                    self.log_textbox.append_log(f"  • 系统指纹: {summary['system_fingerprint']} 项", "INFO")
                if summary['network_fingerprint'] > 0:
                    self.log_textbox.append_log(f"  • 网络指纹: {summary['network_fingerprint']} 项", "INFO")
                if summary['registry_entries'] > 0:
                    self.log_textbox.append_log(f"  • 注册表项: {summary['registry_entries']} 项", "INFO")
                if summary['files_modified'] > 0:
                    self.log_textbox.append_log(f"  • 修改文件: {summary['files_modified']} 项", "INFO")

            # 显示详细结果
            for component in result['components_reset']:
                self.log_textbox.append_log(f"✅ {component}", "SUCCESS")

            # 显示备份信息
            if result['backup_created']:
                self.log_textbox.append_log("💾 设备指纹数据已备份，可在需要时恢复", "INFO")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            # 显示重要提示
            messagebox.showinfo(
                "设备指纹清理完成",
                "设备指纹清理完成！\n\n" +
                "建议重启计算机以确保所有更改生效。\n" +
                "现在您的设备指纹已完全重置，有助于解决账号锁定问题。\n\n" +
                "⚠️ 重要提示：\n" +
                "• 请重启浏览器和相关应用\n" +
                "• 某些网络设置可能需要重新配置\n" +
                "• 如有问题可从备份恢复数据"
            )

        def error_callback(error):
            self.device_fingerprint_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"设备指纹清理失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.device_fingerprint_btn.set_processing(True, "清理中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始设备指纹清理...", "INFO")

        self.current_operation = ThreadedOperation(
            run_fingerprint_clean,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def augment_account_reset(self):
        """Augment账号重置 - 将停用账号恢复为全新状态"""
        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 首先显示IDE选择对话框
        try:
            from .ide_selector_dialog import IDESelectorDialog

            # 创建临时重置器来检测可用的IDE
            temp_resetter = AugmentAccountResetter("auto")
            available_ides = temp_resetter.get_detected_ides()

            # 显示IDE选择对话框
            dialog = IDESelectorDialog(self, available_ides)
            self.wait_window(dialog)

            selected_ide = dialog.get_result()
            if not selected_ide:
                return  # 用户取消了选择

            # 使用选择的IDE类型创建重置器
            account_resetter = AugmentAccountResetter(selected_ide)

        except Exception as e:
            messagebox.showerror("错误", f"IDE选择失败: {str(e)}")
            return

        # 获取账号状态分析
        try:
            analysis = account_resetter.analyze_augment_account_status()
            preview = account_resetter.get_reset_preview()

            status_text = {
                'trial_data_present': '检测到试用数据',
                'used_before': '检测到使用历史',
                'extension_only': '仅安装扩展',
                'clean': '账号状态干净',
                'analysis_failed': '分析失败',
                'unknown': '状态未知'
            }

            current_status = status_text.get(analysis['account_status'], '未知状态')

            # 构建分析信息
            analysis_info = f"当前Augment账号状态: {current_status}\n\n"

            if analysis['risk_factors']:
                analysis_info += "检测到的风险因素:\n"
                for factor in analysis['risk_factors']:
                    analysis_info += f"• {factor}\n"
                analysis_info += "\n"

            if preview['estimated_items'] > 0:
                analysis_info += f"预计重置项目: {preview['estimated_items']} 项\n"
                reset_plan = preview['reset_plan']
                if reset_plan['extension_data'] > 0:
                    analysis_info += f"• 扩展数据: {reset_plan['extension_data']} 项\n"
                if reset_plan['storage_entries'] > 0:
                    analysis_info += f"• 存储条目: {reset_plan['storage_entries']} 项\n"
                if reset_plan['cache_files'] > 0:
                    analysis_info += f"• 缓存文件: {reset_plan['cache_files']} 项\n"
                if reset_plan['config_files'] > 0:
                    analysis_info += f"• 配置文件: {reset_plan['config_files']} 项\n"
                if reset_plan['trial_status'] > 0:
                    analysis_info += f"• 试用状态: {reset_plan['trial_status']} 项\n"

        except Exception as e:
            messagebox.showerror("错误", f"账号状态分析失败: {str(e)}")
            return

        # 获取重置目标信息
        target_info = account_resetter.get_reset_target_info()

        # 确认对话框
        confirm_message = f"{analysis_info}\n"
        confirm_message += "🔄 Augment账号重置功能说明:\n\n"

        # 显示选择的IDE信息
        ide_type_names = {
            'vscode': 'VSCode + Augment插件',
            'cursor': 'Cursor IDE',
            'auto': '自动检测所有IDE'
        }
        selected_ide_name = ide_type_names.get(selected_ide, selected_ide)
        confirm_message += f"🎯 重置目标: {selected_ide_name}\n"

        # 显示检测到的IDE
        detected_ides = []
        if target_info['available_ides']['vscode']:
            detected_ides.append("VSCode")
        if target_info['available_ides']['cursor']:
            detected_ides.append("Cursor")

        if detected_ides:
            confirm_message += f"✅ 检测到的IDE: {', '.join(detected_ides)}\n\n"
        else:
            confirm_message += "⚠️ 未检测到任何IDE\n\n"

        # 显示警告信息
        if target_info['warnings']:
            confirm_message += "⚠️ 注意事项:\n"
            for warning in target_info['warnings']:
                confirm_message += f"• {warning}\n"
            confirm_message += "\n"

        confirm_message += "✨ 核心功能:\n"
        confirm_message += "• 将停用的免费账号恢复为全新未使用状态\n"
        confirm_message += "• 清理所有试用记录和使用历史\n"
        confirm_message += "• 重置设备标识和扩展数据\n"
        confirm_message += "• 避免重新注册的风险\n\n"
        confirm_message += "🔧 重置内容:\n"
        if selected_ide in ['vscode', 'auto'] and target_info['available_ides']['vscode']:
            confirm_message += "• 清理VSCode扩展存储数据\n"
            confirm_message += "• 重置VSCode配置文件\n"
        if selected_ide in ['cursor', 'auto'] and target_info['available_ides']['cursor']:
            confirm_message += "• 清理Cursor存储数据\n"
            confirm_message += "• 重置Cursor机器ID\n"
        confirm_message += "• 清理缓存和日志\n"
        confirm_message += "• 生成新的设备标识\n"
        confirm_message += "• 清理浏览器试用状态\n\n"
        confirm_message += "🎯 预期效果:\n"
        confirm_message += "• 账号状态恢复为全新状态\n"
        confirm_message += "• 可以重新开始试用\n"
        confirm_message += "• 避免账号锁定问题\n"
        confirm_message += "• 无需重新注册\n\n"
        confirm_message += "⚠️ 注意：此操作会清理所有Augment相关数据\n"
        confirm_message += "操作前会自动备份数据。\n\n是否继续？"

        result = messagebox.askyesno("Augment账号重置确认", confirm_message)

        if not result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_account_reset():
            try:
                # 检查是否启用2.0版本
                version_2_enabled = os.environ.get('AUGMENT_VERSION') == '2.0' or os.environ.get('AUGMENT_DEEP_RESET') == '1'

                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "augment_account_backup")

                if version_2_enabled:
                    # 使用2.0版本的重置方法
                    return account_resetter.reset_augment_account_status_v2(backup_dir)
                else:
                    # 使用标准重置方法
                    return account_resetter.reset_augment_account_status(backup_dir)
            except Exception as e:
                raise e

        def success_callback(result):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.update_progress(100, "Augment账号重置完成")

            # 检查是否是2.0版本
            version = result.get('version', '1.0')
            if version == '2.0':
                self.log_textbox.append_log("🚀 AugmentNew 2.0 终极重置完成！", "SUCCESS")
                self.log_textbox.append_log(f"应用了 {len(result.get('techniques_applied', []))} 种最新技术", "INFO")

                # 显示2.0版本技术摘要
                techniques = result.get('techniques_applied', [])
                if techniques:
                    self.log_textbox.append_log("🔥 应用的2.0技术:", "INFO")
                    for technique in techniques:
                        self.log_textbox.append_log(f"  ✅ {technique}", "SUCCESS")
            else:
                self.log_textbox.append_log("🔄 Augment账号重置完成！", "SUCCESS")

            self.log_textbox.append_log(f"重置了 {len(result['reset_components'])} 个组件", "INFO")

            # 显示IDE信息
            ide_info = result.get('ide_info', {})
            if ide_info:
                ide_type_names = {
                    'vscode': 'VSCode + Augment插件',
                    'cursor': 'Cursor IDE',
                    'auto': '自动检测模式'
                }
                selected_name = ide_type_names.get(ide_info.get('ide_type'), ide_info.get('ide_type', '未知'))
                self.log_textbox.append_log(f"🎯 重置模式: {selected_name}", "INFO")

            # 显示重置摘要
            summary = result['reset_summary']
            if any(summary.values()):
                self.log_textbox.append_log("📊 重置摘要:", "INFO")
                if summary['extension_data'] > 0:
                    self.log_textbox.append_log(f"  • 扩展数据: {summary['extension_data']} 项", "INFO")
                if summary['storage_entries'] > 0:
                    self.log_textbox.append_log(f"  • 存储条目: {summary['storage_entries']} 项", "INFO")
                if summary['cache_files'] > 0:
                    self.log_textbox.append_log(f"  • 缓存文件: {summary['cache_files']} 项", "INFO")
                if summary['config_files'] > 0:
                    self.log_textbox.append_log(f"  • 配置文件: {summary['config_files']} 项", "INFO")
                if summary['trial_status'] > 0:
                    self.log_textbox.append_log(f"  • 试用状态: {summary['trial_status']} 项", "INFO")

            # 显示详细结果
            for component in result['reset_components']:
                self.log_textbox.append_log(f"✅ {component}", "SUCCESS")

            # 显示备份信息
            if result['backup_created']:
                self.log_textbox.append_log("💾 账号数据已备份，可在需要时恢复", "INFO")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors']:
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            # 显示重要提示
            ide_info = result.get('ide_info', {})
            ide_type = ide_info.get('ide_type', 'auto')

            restart_instructions = []
            if ide_type in ['vscode', 'auto'] and ide_info.get('available_ides', {}).get('vscode', False):
                restart_instructions.append("• 请重启VSCode")
            if ide_type in ['cursor', 'auto'] and ide_info.get('available_ides', {}).get('cursor', False):
                restart_instructions.append("• 请重启Cursor")

            if not restart_instructions:
                restart_instructions = ["• 请重启相关IDE"]

            messagebox.showinfo(
                "Augment账号重置完成",
                "Augment账号重置完成！\n\n" +
                "您的Augment账号现在已恢复为全新未使用状态。\n" +
                "现在可以重新登录并开始使用，无需重新注册！\n\n" +
                "🎉 主要效果：\n" +
                "• 试用状态已重置\n" +
                "• 使用历史已清理\n" +
                "• 设备标识已更新\n" +
                "• 避免了账号锁定\n\n" +
                "⚠️ 重要提示：\n" +
                "\n".join(restart_instructions) + "\n" +
                "• 重新登录Augment账号\n" +
                "• 如有问题可从备份恢复数据"
            )

        def error_callback(error):
            self.augment_account_reset_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"Augment账号重置失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"操作失败: {str(error)}")

        self.augment_account_reset_btn.set_processing(True, "重置中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始Augment账号重置...", "INFO")

        self.current_operation = ThreadedOperation(
            run_account_reset,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def super_reset_engine(self):
        """超级重置引擎 - 最强力的系统重置"""
        if not self.super_config['super_mode']:
            messagebox.showwarning("功能限制", "超级重置引擎仅在超级模式下可用")
            return

        if self.current_operation and self.current_operation.is_running():
            messagebox.showwarning("警告", "有操作正在进行中，请等待完成")
            return

        # 显示超级重置警告
        warning_message = "💥 超级重置引擎 💥\n\n"
        warning_message += "⚠️ 这是最强力的系统重置功能！\n\n"
        warning_message += "🔥 核弹级重置内容:\n"
        warning_message += "• 深度注册表清理\n"
        warning_message += "• WMI系统重置\n"
        warning_message += "• 硬件指纹核弹级重置\n"
        warning_message += "• 网络栈核弹级重置\n"
        warning_message += "• 系统缓存深度清理\n"
        warning_message += "• 加密密钥重置\n"
        warning_message += "• 事件日志核弹级清理\n"
        warning_message += "• 预取文件重置\n"
        warning_message += "• 缩略图缓存重置\n"
        warning_message += "• 字体缓存重置\n"
        warning_message += "• DNS缓存深度清理\n"
        warning_message += "• 临时文件核弹级清理\n"
        warning_message += "• 用户配置文件选择性重置\n"
        warning_message += "• Windows搜索索引重置\n\n"
        warning_message += "🛡️ 安全保护:\n"
        warning_message += "• 自动创建系统还原点\n"
        warning_message += "• 全面数据备份\n"
        warning_message += "• 完整性验证\n"
        warning_message += "• 错误恢复机制\n\n"
        warning_message += "⚡ 预期效果:\n"
        warning_message += "• 系统状态完全重置\n"
        warning_message += "• 所有软件试用状态清零\n"
        warning_message += "• 设备指纹彻底更新\n"
        warning_message += "• 网络身份完全重置\n\n"
        warning_message += "🚨 重要警告:\n"
        warning_message += "• 此操作不可逆转\n"
        warning_message += "• 需要管理员权限\n"
        warning_message += "• 建议关闭所有程序\n"
        warning_message += "• 操作完成后需要重启\n\n"
        warning_message += "确定要执行超级重置吗？"

        result = messagebox.askyesno("超级重置引擎确认", warning_message)
        if not result:
            return

        # 二次确认
        confirm_message = "🔥 最后确认 🔥\n\n"
        confirm_message += "您即将执行核弹级系统重置！\n\n"
        confirm_message += "此操作将:\n"
        confirm_message += "• 重置所有系统标识\n"
        confirm_message += "• 清理所有软件痕迹\n"
        confirm_message += "• 重建系统指纹\n"
        confirm_message += "• 恢复全新系统状态\n\n"
        confirm_message += "⚠️ 请确保已保存重要工作！\n\n"
        confirm_message += "真的要继续吗？"

        final_result = messagebox.askyesno("最终确认", confirm_message)
        if not final_result:
            return

        def progress_callback(value, status):
            self.progress_card.update_progress(value, status)

        def run_super_reset():
            try:
                super_engine = SuperResetEngine()
                backup_dir = os.path.join(get_home_dir(), "AugmentNew_Backups", "super_reset_backup")
                return super_engine.execute_super_reset("augment", backup_dir)
            except Exception as e:
                raise e

        def success_callback(result):
            if hasattr(self, 'super_reset_btn'):
                self.super_reset_btn.set_processing(False)
            self.progress_card.update_progress(100, "超级重置完成")

            self.log_textbox.append_log("💥 超级重置引擎执行完成！", "SUCCESS")
            self.log_textbox.append_log(f"处理了 {len(result['reset_components'])} 个组件", "INFO")

            # 显示重置摘要
            summary = result['reset_summary']
            self.log_textbox.append_log("📊 超级重置摘要:", "INFO")
            if summary['registry_entries'] > 0:
                self.log_textbox.append_log(f"  • 注册表项: {summary['registry_entries']} 个", "INFO")
            if summary['files_processed'] > 0:
                self.log_textbox.append_log(f"  • 处理文件: {summary['files_processed']} 个", "INFO")
            if summary['directories_cleaned'] > 0:
                self.log_textbox.append_log(f"  • 清理目录: {summary['directories_cleaned']} 个", "INFO")
            if summary['wmi_objects_reset'] > 0:
                self.log_textbox.append_log(f"  • WMI对象: {summary['wmi_objects_reset']} 个", "INFO")
            if summary['crypto_keys_reset'] > 0:
                self.log_textbox.append_log(f"  • 加密密钥: {summary['crypto_keys_reset']} 个", "INFO")
            if summary['fingerprints_reset'] > 0:
                self.log_textbox.append_log(f"  • 硬件指纹: {summary['fingerprints_reset']} 个", "INFO")

            # 显示安全检查结果
            safety = result['safety_checks']
            self.log_textbox.append_log("🛡️ 安全检查结果:", "INFO")
            if safety['backup_verified']:
                self.log_textbox.append_log("  ✅ 数据备份已验证", "SUCCESS")
            if safety['restore_point_verified']:
                self.log_textbox.append_log("  ✅ 系统还原点已创建", "SUCCESS")
            if safety['system_integrity_maintained']:
                self.log_textbox.append_log("  ✅ 系统完整性已维护", "SUCCESS")

            # 显示详细结果
            for component in result['reset_components'][:20]:  # 只显示前20个
                self.log_textbox.append_log(f"✅ {component}", "SUCCESS")

            if len(result['reset_components']) > 20:
                self.log_textbox.append_log(f"... 还有 {len(result['reset_components']) - 20} 个组件", "INFO")

            if result['errors']:
                self.log_textbox.append_log("遇到的错误:", "WARNING")
                for error in result['errors'][:10]:  # 只显示前10个错误
                    self.log_textbox.append_log(f"  ❌ {error}", "ERROR")

            # 显示重要提示
            messagebox.showinfo(
                "超级重置完成",
                "💥 超级重置引擎执行完成！💥\n\n" +
                "🎉 系统已完全重置为全新状态！\n\n" +
                "✨ 主要效果：\n" +
                "• 所有软件试用状态已重置\n" +
                "• 设备指纹已彻底更新\n" +
                "• 网络身份已完全重置\n" +
                "• 系统缓存已深度清理\n" +
                "• 注册表已优化重置\n\n" +
                "🔄 下一步操作：\n" +
                "• 立即重启计算机\n" +
                "• 重新安装需要的软件\n" +
                "• 享受全新的系统体验\n\n" +
                "⚠️ 重要提醒：\n" +
                "• 系统已创建还原点\n" +
                "• 数据已完整备份\n" +
                "• 如有问题可恢复数据"
            )

        def error_callback(error):
            if hasattr(self, 'super_reset_btn'):
                self.super_reset_btn.set_processing(False)
            self.progress_card.reset()
            self.log_textbox.append_log(f"超级重置失败: {str(error)}", "ERROR")
            messagebox.showerror("错误", f"超级重置失败: {str(error)}")

        if hasattr(self, 'super_reset_btn'):
            self.super_reset_btn.set_processing(True, "核弹级重置中...")
        self.progress_card.reset()
        self.log_textbox.append_log("开始执行超级重置引擎...", "INFO")
        self.log_textbox.append_log("⚠️ 正在执行核弹级系统重置", "WARNING")

        self.current_operation = ThreadedOperation(
            run_super_reset,
            success_callback,
            error_callback,
            progress_callback
        )
        self.current_operation.start()

    def on_closing(self):
        """窗口关闭事件"""
        if self.current_operation and self.current_operation.is_running():
            result = messagebox.askyesno(
                "确认退出",
                "有操作正在进行中，确定要退出吗？"
            )
            if not result:
                return

        self.destroy()
